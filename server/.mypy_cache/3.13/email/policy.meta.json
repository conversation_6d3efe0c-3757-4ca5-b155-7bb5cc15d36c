{"data_mtime": 1754498048, "dep_lines": [1, 2, 3, 4, 5, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "email._policybase", "email.contentmanager", "email.message", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "3d8a76ddc4e76139fdc5606031768a0eab26f1ea", "id": "email.policy", "ignore_all": true, "interface_hash": "a41a5d12ec59b2bc1db36a037d61665414d00993", "mtime": 1753735512, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "silent", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/email/policy.pyi", "plugin_data": null, "size": 2813, "suppressed": [], "version_id": "1.17.0"}