{".class": "MypyFile", "_fullname": "importlib._bootstrap", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BuiltinImporter": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.BuiltinImporter", "kind": "Gdef"}, "FrozenImporter": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.FrozenImporter", "kind": "Gdef"}, "ModuleSpec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.ModuleSpec", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__import__": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.__import__", "kind": "Gdef"}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_init_module_attrs": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib._init_module_attrs", "kind": "Gdef"}, "module_from_spec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.module_from_spec", "kind": "Gdef"}, "spec_from_loader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.spec_from_loader", "kind": "Gdef"}}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/importlib/_bootstrap.pyi"}