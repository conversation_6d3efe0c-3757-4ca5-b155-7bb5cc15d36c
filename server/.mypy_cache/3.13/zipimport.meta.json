{"data_mtime": 1754498048, "dep_lines": [3, 8, 1, 2, 4, 5, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.machinery", "importlib.readers", "sys", "_typeshed", "types", "typing_extensions", "_frozen_importlib_external", "builtins", "_frozen_importlib", "abc", "importlib", "importlib.resources", "importlib.resources.abc", "typing", "warnings"], "hash": "823c44e8278a8d36fd11aea3ea656af63f1bfc00", "id": "zipimport", "ignore_all": true, "interface_hash": "c58c7f56d4d3b78887dfcad29e0fb137630fcc8e", "mtime": 1753735516, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "silent", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "/home/<USER>/dev/ued/server/.venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/zipimport.pyi", "plugin_data": null, "size": 2079, "suppressed": [], "version_id": "1.17.0"}