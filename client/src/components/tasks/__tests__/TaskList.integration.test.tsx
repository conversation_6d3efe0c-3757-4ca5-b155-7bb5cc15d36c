/**
 * Integration tests for TaskList component
 * Tests React Query integration, component behavior, and data flow
 */

import React from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { TaskList } from "../TaskList"

// Define types locally to avoid import issues
interface TaskRead {
  task_id: string
  project_id: number
  title: string
  description?: string
  priority: "Low" | "Medium" | "High" | "Critical"
  status:
    | "Not Started"
    | "In Progress"
    | "On Hold"
    | "Completed"
    | "Blocked"
    | "Overdue"
    | "Review Pending"
    | "Approved"
  due_date?: string | null
  created_at: string
  updated_at: string
  assignments?: Array<{
    id: number
    user_id: number
    task_id: string
    assigned_at: string
    user?: {
      id: number
      name: string
      email: string
    }
  }>
}

// Mock the task API
const mockTaskApi = {
  list: vi.fn(),
  getById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
}

vi.mock("@/api/tasks", () => ({
  taskApi: mockTaskApi,
}))

// Mock WebSocket hook
vi.mock("@/hooks/useTaskWebSocket", () => ({
  useTaskWebSocket: vi.fn(() => ({
    connectionState: "connected",
    isConnected: true,
  })),
}))

// Mock data
const mockTasks: TaskRead[] = [
  {
    task_id: "task-1",
    project_id: 1,
    title: "Test Task 1",
    description: "Description for task 1",
    priority: "High",
    status: "In Progress",
    due_date: "2024-12-31",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    assignments: [],
  },
  {
    task_id: "task-2",
    project_id: 1,
    title: "Test Task 2",
    description: "Description for task 2",
    priority: "Medium",
    status: "Not Started",
    due_date: "2024-12-25",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    assignments: [],
  },
  {
    task_id: "task-3",
    project_id: 1,
    title: "Test Task 3",
    description: "Description for task 3",
    priority: "Low",
    status: "Completed",
    due_date: null,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    assignments: [],
  },
]

// Test utilities
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <QueryClientProvider client={createTestQueryClient()}>
    {children}
  </QueryClientProvider>
)

describe("TaskList Integration Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockTaskApi.list.mockResolvedValue({
      data: mockTasks,
      status: 200,
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Basic Rendering and Data Loading", () => {
    it("renders task list with data from React Query", async () => {
      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Wait for tasks to load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
        expect(screen.getByText("Test Task 2")).toBeInTheDocument()
        expect(screen.getByText("Test Task 3")).toBeInTheDocument()
      })

      // Verify API was called
      expect(mockTaskApi.list).toHaveBeenCalledWith(
        expect.objectContaining({
          projectId: 1,
        })
      )
    })

    it("displays loading state while fetching data", async () => {
      // Mock delayed response
      mockTaskApi.list.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(() => resolve({ data: mockTasks, status: 200 }), 100)
          )
      )

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Check loading state
      expect(screen.getByTestId("task-list-loading")).toBeInTheDocument()

      // Wait for data to load
      await waitFor(() => {
        expect(
          screen.queryByTestId("task-list-loading")
        ).not.toBeInTheDocument()
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })
    })

    it("displays error state when API call fails", async () => {
      mockTaskApi.list.mockRejectedValue(new Error("API Error"))

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId("task-list-error")).toBeInTheDocument()
        expect(screen.getByText(/error loading tasks/i)).toBeInTheDocument()
      })
    })
  })

  describe("Filtering and Search", () => {
    it("applies status filter and refetches data", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })

      // Apply status filter
      const statusFilter = screen.getByTestId("status-filter")
      await user.selectOptions(statusFilter, "In Progress")

      // Verify API called with filter
      await waitFor(() => {
        expect(mockTaskApi.list).toHaveBeenCalledWith(
          expect.objectContaining({
            projectId: 1,
            filters: expect.objectContaining({
              status: ["In Progress"],
            }),
          })
        )
      })
    })

    it("applies search term and refetches data", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })

      // Apply search
      const searchInput = screen.getByTestId("task-search")
      await user.type(searchInput, "Task 1")

      // Verify API called with search
      await waitFor(() => {
        expect(mockTaskApi.list).toHaveBeenCalledWith(
          expect.objectContaining({
            projectId: 1,
            filters: expect.objectContaining({
              search: "Task 1",
            }),
          })
        )
      })
    })
  })

  describe("Sorting", () => {
    it("applies sort and refetches data", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })

      // Apply sort
      const sortSelect = screen.getByTestId("sort-select")
      await user.selectOptions(sortSelect, "priority-desc")

      // Verify API called with sort
      await waitFor(() => {
        expect(mockTaskApi.list).toHaveBeenCalledWith(
          expect.objectContaining({
            projectId: 1,
            sort: expect.objectContaining({
              field: "priority",
              order: "desc",
            }),
          })
        )
      })
    })
  })

  describe("Task Interactions", () => {
    it("handles task selection", async () => {
      const user = userEvent.setup()
      const onTaskSelect = vi.fn()

      render(
        <TestWrapper>
          <TaskList projectId={1} onTaskSelect={onTaskSelect} />
        </TestWrapper>
      )

      // Wait for tasks to load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })

      // Click on a task
      const taskCard = screen.getByTestId("task-card-task-1")
      await user.click(taskCard)

      expect(onTaskSelect).toHaveBeenCalledWith(mockTasks[0])
    })

    it("handles refresh action", async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText("Test Task 1")).toBeInTheDocument()
      })

      // Click refresh button
      const refreshButton = screen.getByTestId("refresh-tasks")
      await user.click(refreshButton)

      // Verify API called again
      expect(mockTaskApi.list).toHaveBeenCalledTimes(2)
    })
  })

  describe("Real-time Updates", () => {
    it("integrates with WebSocket for real-time updates", async () => {
      const { useTaskWebSocket } = await import("@/hooks/useTaskWebSocket")

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      // Verify WebSocket hook is called with correct project ID
      expect(useTaskWebSocket).toHaveBeenCalledWith(1, expect.any(Object))
    })
  })

  describe("Empty States", () => {
    it("displays empty state when no tasks exist", async () => {
      mockTaskApi.list.mockResolvedValue({
        data: [],
        status: 200,
      })

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId("task-list-empty")).toBeInTheDocument()
        expect(screen.getByText(/no tasks found/i)).toBeInTheDocument()
      })
    })

    it("displays filtered empty state when filters return no results", async () => {
      mockTaskApi.list.mockResolvedValue({
        data: [],
        status: 200,
      })

      render(
        <TestWrapper>
          <TaskList projectId={1} />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByTestId("task-list-empty")).toBeInTheDocument()
      })
    })
  })
})
